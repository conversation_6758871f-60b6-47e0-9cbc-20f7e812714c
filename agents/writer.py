"""
Writer Agent
-----------
Creates Hindi documentary-style narration from structured research reports.
"""

import os
import json
import logging

from crewai import Agent, Task, Crew, Process

from utils.agent_factory import create_rate_limited_agent
from utils.parsers import StoryParser
from utils.content_moderation import sanitize_prompt
from models.schema import Story, StructuredReport

logger = logging.getLogger(__name__)

class WriterAgent:
    """
    Agent for creating Hindi documentary-style narration from structured research reports.

    This agent takes structured research reports and creates compelling Hindi documentary
    narrations inspired by popular Hindi documentary creators like <PERSON><PERSON><PERSON>.
    """

    def __init__(self,
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai") -> None:
        """
        Initialize the Writer Agent.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI. Defaults to False.
            model (str): The model to use for the agent. Defaults to "gpt-4o-mini".
            provider (str): The LLM provider to use. Defaults to "openai".
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

        logger.info(f"WriterAgent initialized with model: {model}, provider: {provider}")

    def write_documentary_story(self,
                               structured_report: StructuredReport,
                               title: str) -> Story:
        """
        Create a Hindi documentary-style narration from a structured research report.

        Args:
            structured_report (StructuredReport): The structured research report
            title (str): The title of the story

        Returns:
            Story: Story data in structured format using Pydantic model
        """
        logger.info(f"Creating documentary narration for title: '{title}'")

        # Create a parser for the Story model
        parser = StoryParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the documentary writer agent
        writer = create_rate_limited_agent(
            role="Hindi Docudrama Scriptwriter",
            goal="To write immersive, emotionally-driven docudrama scripts in Hindi (Devanagari script) that vividly reconstruct real-life incidents with cinematic storytelling, factual accuracy, and cultural depth.",
            backstory="""You are a professional Hindi docudrama scriptwriter with a strong sense of ethics, realism, and cinematic storytelling. Your job is to transform researched facts into emotionally resonant scripts that feel like real events unfolding on screen.

            Your scripts are designed for voiceover narration with rich visual descriptions, and they are structured to reflect dramatic arcs — from powerful openings to reflective closings. You never use words that imply fiction like "कहानी" or "किस्सा", and you respect the seriousness of real-life events, especially those involving loss, injustice, or human struggle.

            Your Hindi language is expressive, poetic where needed, but always precise and grounded in Indian cultural context. Each script you write blends factual narration with immersive scene-building and emotional storytelling, as if the viewer is inside the moment.

            You understand pacing, transitions, and narrative structure, ensuring every scene has a clear emotional and narrative purpose — whether it's setting the context, building tension, presenting a twist, or delivering resolution.

            You strictly avoid fictionalization unless it's explicitly noted in the research, and your tone remains respectful, insightful, and thought-provoking throughout the script.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False
        )

        # Convert structured report to string for the task
        report_str = json.dumps(structured_report.model_dump(), ensure_ascii=False, indent=2)

        # Create the documentary writing task
        task_description = f"""
        Write a cinematic, emotionally-driven **docudrama script** in Hindi (Devanagari script) based on the researched content provided below. The script should recreate real-life incidents through dramatic storytelling, grounded realism, and powerful narration — as if the audience is witnessing the event unfold before them.

        {report_str}

        Title: "{title}"

        Requirements:
        1. The **entire narration must be in Hindi (Devanagari script)** — in a voiceover or reflective tone.
        2. The tone should reflect **emotional depth, realism, and reverence** for the subject matter.
        3. Avoid fictionalization or dramatization unless it is **explicitly supported** by the research.
        4. **Do not use words like "कहानी", "किस्सा", or any terms that imply fiction**.
        5. The narration should feel like a **first-hand reflection or cinematic reconstruction** — not a fictional story.

        Tone & Style:
        - Blend **verified facts** with **dramatic visual narration**.
        - Use **sensory and atmospheric details**: sounds, light, weather, emotions, environments.
        - Begin with a **powerful hook** — a line that raises tension or curiosity.
        - Use **emotional arcs** — build suspense, offer emotional insight, and guide the viewer through high and low points.
        - Include **cultural depth, Indian context, and symbolic visual cues**.
        - Keep any dialogues **short, natural**, and embedded within the narration (not screenplay style).

        Important:
        - This is a **docudrama**, not a documentary — emphasize **emotionally engaging recreation**, not just factual reporting.
        - Scenes should **feel like moments from a recreated film**, not an explainer.
        - Maintain **tight scene-to-scene transitions** for smooth narrative flow.
        - Conclude with a **reflective line or moral takeaway** that connects back to the broader theme or legacy of the event.
        """

        # Create the documentary writing task
        writing_task = Task(
            description=task_description,
            agent=writer,
            expected_output=format_instructions
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[writing_task],
            agents=[writer],
            verbose=self.verbose
        )

        try:
            crew_output = crew.kickoff()
            result = crew_output.raw

            # Apply content moderation to the raw result before parsing
            sanitized_result = sanitize_prompt(result)

            # Parse the result using the Pydantic parser
            story = StoryParser.parse_output(parser, sanitized_result)

            # If parsing fails, raise an error
            if story is None:
                logger.error("Could not parse Writer result, Raw output: %s", result)
                raise ValueError("Failed to parse story from Writer Agent")

            logger.info(f"Documentary story creation completed successfully with {len(story.scenes)} scenes")
            return story

        except Exception as e:
            logger.error(f"Error creating documentary story: {str(e)}")
            raise
